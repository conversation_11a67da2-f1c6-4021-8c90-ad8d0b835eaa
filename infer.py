import transformers
import torch
import random
from datasets import load_dataset
import requests

from search_r1.search.codefuse.prompt_template import swe_prompt_template
from search_r1.search.codefuse import SearchFn

org = "checkstyle"
repo = "checkstyle"
repo = org + '/' + repo
search_tool_schemas = SearchFn.search_tool_schemas_str
issues = "I need to process one or more issues in the checkstyle/checkstyle repository. The following are the titles of these issues:\n[Issue 1: Add Check Support for Java 21 Pattern Matching for Switch Syntax: SimplifyBooleanReturn]\nPlease provide the key code snippets required to resolve these issues."
ref = "master"
repo_url = 'https://code.alipay.com/{}.git'.format(repo)
max_turns = 10

# Model ID and device setup
model_id = "/mnt2/user/445320/Qwen2.5-Coder-14B-Instruct"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

question = question.strip()
if question[-1] != '?':
    question += '?'
curr_eos = [151645, 151643] # for Qwen2.5 series models
curr_search_template = '\n\n{output_text}<information>{search_results}</information>\n\n'

# Prepare the message
prompt = f"""
Your task is to identify the key code snippets to resolve one or more issues within code repository {repo}. 
You are allowed to use the following search tools to access external knowledge within the code repository:
{search_tool_schemas}
You must conduct reasoning inside <think> and </think> every time you get new information(including previous searched results). 
After reasoning, if you find you lack some knowledge, you can call a search tool between <search> and </search>,
for example, <search> {{"search_tool": "SemanticCodeSearch", "search_params": {{"query": "your query here"}}}} </search>.
And it will return the top searched results between <information> and </information>, for example, <information> [{{"source": "codebase", "path": "path/to/the/code", "code": "code snippet here"}}, ...] </information>.
You can search at most **ten** times, then you must provide the answer. 
Or if you find no further external knowledge needed, you can directly provide the the key code snippets to resolve the issues as a list inside <answer> and </answer>, the code snippets should be from previous searched results.
For example, <answer> [{{"source": "codebase", "path": "path/to/the/code", "code": "code snippet here"}}, ...] </answer>.
The following are the titles of the issues: {issues}
"""

# Initialize the tokenizer and model
tokenizer = transformers.AutoTokenizer.from_pretrained(model_id)
model = transformers.AutoModelForCausalLM.from_pretrained(model_id, torch_dtype=torch.bfloat16, device_map="auto")

# Define the custom stopping criterion
class StopOnSequence(transformers.StoppingCriteria):
    def __init__(self, target_sequences, tokenizer):
        # Encode the string so we have the exact token-IDs pattern
        self.target_ids = [tokenizer.encode(target_sequence, add_special_tokens=False) for target_sequence in target_sequences]
        self.target_lengths = [len(target_id) for target_id in self.target_ids]
        self._tokenizer = tokenizer

    def __call__(self, input_ids, scores, **kwargs):
        # Make sure the target IDs are on the same device
        targets = [torch.as_tensor(target_id, device=input_ids.device) for target_id in self.target_ids]

        if input_ids.shape[1] < min(self.target_lengths):
            return False

        # Compare the tail of input_ids with our target_ids
        for i, target in enumerate(targets):
            if torch.equal(input_ids[0, -self.target_lengths[i]:], target):
                return True

        return False

def get_query(text):
    import re
    pattern = re.compile(r"<search>(.*?)</search>", re.DOTALL)
    matches = pattern.findall(text)
    if matches:
        return matches[-1]
    else:
        return None

def search(query: str):
    query = json.loads(query)
    search_tool_list = [query["search_tool"]]
    search_params_list = [{**query["search_params"], **{'extra_info': {'branch': ref, 'index': index, 'repo_url': repo_url}}}]
    results = SearchFn.batch_do(
        search_tool_list=search_tool_list,
        search_params_list=search_params_list
    )[0]
    return json.dumps(results,ensure_ascii=False)

# Initialize the stopping criteria
target_sequences = ["</search>", " </search>", "</search>\n", " </search>\n", "</search>\n\n", " </search>\n\n"]
stopping_criteria = transformers.StoppingCriteriaList([StopOnSequence(target_sequences, tokenizer)])

cnt = 0

if tokenizer.chat_template:
    prompt = tokenizer.apply_chat_template([{"role": "user", "content": prompt}], add_generation_prompt=True, tokenize=False)

print('\n\n################# [Start Reasoning + Searching] ##################\n\n')
print(prompt)
# Encode the chat-formatted prompt and move it to the correct device
while cnt <= max_turns:
    input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
    attention_mask = torch.ones_like(input_ids)
    
    # Generate text with the stopping criteria
    outputs = model.generate(
        input_ids,
        attention_mask=attention_mask,
        max_new_tokens=8192,
        stopping_criteria=stopping_criteria,
        pad_token_id=tokenizer.eos_token_id,
        do_sample=True,
        temperature=0.7
    )

    if outputs[0][-1].item() in curr_eos:
        generated_tokens = outputs[0][input_ids.shape[1]:]
        output_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)
        print(output_text)
        break

    generated_tokens = outputs[0][input_ids.shape[1]:]
    output_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)
    
    tmp_query = get_query(tokenizer.decode(outputs[0], skip_special_tokens=True))
    if tmp_query:
        # print(f'searching "{tmp_query}"...')
        search_results = search(tmp_query)
    else:
        search_results = ''

    cnt += 1
    if cnt == max_turns:
        search_results += '<answer>'
    search_text = curr_search_template.format(output_text=output_text, search_results=search_results)
    prompt += search_text
    print(search_text)
