{"os": "Linux-4.9.151-015.ali3000.alios7.x86_64-x86_64-with-glibc2.32", "python": "CPython 3.10.13", "startedAt": "2025-04-28T13:06:32.913921Z", "args": ["--node-ip-address=*************", "--node-manager-port=44353", "--object-store-name=/tmp/ray/session_2025-04-28_21-06-06_065641_222576/sockets/plasma_store", "--raylet-name=/tmp/ray/session_2025-04-28_21-06-06_065641_222576/sockets/raylet", "--redis-address=None", "--metrics-agent-port=63442", "--logging-rotate-bytes=536870912", "--logging-rotate-backup-count=5", "--runtime-env-agent-port=41733", "--gcs-address=*************:60406", "--session-name=session_2025-04-28_21-06-06_065641_222576", "--temp-dir=/tmp/ray", "--webui=127.0.0.1:8265", "--cluster-id=0e4a8bb434001ae2fb574bb18e5f8aeb208b7bb78cc1566fb804f6e5", "--startup-token=11", "--worker-launch-time-ms=1745845568837", "--node-id=b79a6777173859c9e19ddd7270c207904fd2bd9d953e291294e0de6a", "--runtime-env-hash=1830736042"], "program": "/opt/conda/lib/python3.10/site-packages/ray/_private/workers/default_worker.py", "git": {"remote": "*******************:dev_alg/Search-R1.git", "commit": "df7879ff7a0c16a50be8924c5f3bfc42b9b665e5"}, "root": "/ossfs/workspace/yuyichen.yyc/Search-R1", "host": "deveffinlptraingpu-55-033211213025", "executable": "/opt/conda/bin/python3", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 1, "disk": {"/": {"total": "260919263232", "used": "15446831104"}}, "memory": {"total": "53687091200"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85198045184", "architecture": "Ampere"}], "cudaVersion": "12.4"}