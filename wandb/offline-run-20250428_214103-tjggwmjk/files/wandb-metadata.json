{"os": "Linux-4.9.151-015.ali3000.alios7.x86_64-x86_64-with-glibc2.32", "python": "CPython 3.10.13", "startedAt": "2025-04-28T13:41:04.988448Z", "args": ["--node-ip-address=*************", "--node-manager-port=39313", "--object-store-name=/tmp/ray/session_2025-04-28_21-40-38_083813_257155/sockets/plasma_store", "--raylet-name=/tmp/ray/session_2025-04-28_21-40-38_083813_257155/sockets/raylet", "--redis-address=None", "--metrics-agent-port=57954", "--logging-rotate-bytes=536870912", "--logging-rotate-backup-count=5", "--runtime-env-agent-port=59956", "--gcs-address=*************:64522", "--session-name=session_2025-04-28_21-40-38_083813_257155", "--temp-dir=/tmp/ray", "--webui=127.0.0.1:8265", "--cluster-id=58f0cd938c125b5c3a5cf26ddc3cec547a5135d734e419332d5a6959", "--startup-token=11", "--worker-launch-time-ms=1745847641494", "--node-id=d70e96393742f7c5829772b025f51e832e0e8981d4d0a199c49c17a7", "--runtime-env-hash=1830736042"], "program": "/opt/conda/lib/python3.10/site-packages/ray/_private/workers/default_worker.py", "git": {"remote": "*******************:dev_alg/Search-R1.git", "commit": "df7879ff7a0c16a50be8924c5f3bfc42b9b665e5"}, "root": "/ossfs/workspace/yuyichen.yyc/Search-R1", "host": "deveffinlptraingpu-55-033211213025", "executable": "/opt/conda/bin/python3", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 1, "disk": {"/": {"total": "260919263232", "used": "15451414528"}}, "memory": {"total": "53687091200"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85198045184", "architecture": "Ampere"}], "cudaVersion": "12.4"}