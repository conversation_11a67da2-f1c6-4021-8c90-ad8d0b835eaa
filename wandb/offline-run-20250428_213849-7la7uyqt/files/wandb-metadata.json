{"os": "Linux-4.9.151-015.ali3000.alios7.x86_64-x86_64-with-glibc2.32", "python": "CPython 3.10.13", "startedAt": "2025-04-28T13:38:50.423919Z", "args": ["--node-ip-address=*************", "--node-manager-port=34561", "--object-store-name=/tmp/ray/session_2025-04-28_21-38-23_154022_252199/sockets/plasma_store", "--raylet-name=/tmp/ray/session_2025-04-28_21-38-23_154022_252199/sockets/raylet", "--redis-address=None", "--metrics-agent-port=56879", "--logging-rotate-bytes=536870912", "--logging-rotate-backup-count=5", "--runtime-env-agent-port=60405", "--gcs-address=*************:61028", "--session-name=session_2025-04-28_21-38-23_154022_252199", "--temp-dir=/tmp/ray", "--webui=127.0.0.1:8265", "--cluster-id=13158ae7f90147884987052e509219e508c4964356cf750e90d074f2", "--startup-token=11", "--worker-launch-time-ms=1745847506626", "--node-id=e21132fcdb9cd8edb98183c267d3210e162fcacbf7cf22e8d1e1e3f0", "--runtime-env-hash=1830736042"], "program": "/opt/conda/lib/python3.10/site-packages/ray/_private/workers/default_worker.py", "git": {"remote": "*******************:dev_alg/Search-R1.git", "commit": "df7879ff7a0c16a50be8924c5f3bfc42b9b665e5"}, "root": "/ossfs/workspace/yuyichen.yyc/Search-R1", "host": "deveffinlptraingpu-55-033211213025", "executable": "/opt/conda/bin/python3", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 1, "disk": {"/": {"total": "260919263232", "used": "15450226688"}}, "memory": {"total": "53687091200"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85198045184", "architecture": "Ampere"}], "cudaVersion": "12.4"}