verl==0.1
colorama==0.4.6
psutil==7.0.0
setproctitle==1.2.2
verl==0.1
Brotli==1.0.9
Deprecated==1.2.18
PySocks==1.7.1
PyYAML==6.0.2
Send2Trash==1.8.3
accelerate==1.6.0
aii-pypai==0.1.40.73
aiofiles==23.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.10
aiohttp-cors==0.7.0
aiosignal==1.3.1
airportsdata==20241001
aistudio-analyzer==0.0.4.115
aistudio-common==0.0.28.60
aistudio-notebook==2.0.131
aistudio-serving==0.0.0.93
aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-kms==2.16.5
altair==5.5.0
anaconda-anon-usage==0.5.0
annotated-types==0.7.0
anyio==4.7.0
archspec==0.2.3
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
astor==0.8.1
astroid==3.3.6
asttokens==3.0.0
async-timeout==5.0.1
attrs==24.2.0
autopep8==2.0.4
babel==2.16.0
beautifulsoup4==4.12.3
blake3==1.0.4
bleach==6.2.0
blessed==1.20.0
boltons==23.0.0
cachetools==3.1.1
cattrs==24.1.2
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==2.0.4
click==8.1.8
cloudpickle==3.1.0
coloredlogs==15.0.1
colorful==0.5.6
comm==0.2.2
compressed-tensors==0.9.3
concurrent-log-handler==0.9.20
conda==24.11.1
conda-content-trust==0.2.0
conda-libmamba-solver==24.1.0
conda-package-handling==2.4.0
conda_package_streaming==0.11.0
configparser==7.1.0
contourpy==1.3.1
crcmod==1.7
cryptography==43.0.3
cupy-cuda12x==13.4.1
cycler==0.12.1
datasets==3.5.0
debugpy==1.8.9
decorator==5.1.1
defusedxml==0.7.1
deprecation==2.1.0
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
dnspython==2.7.0
docstring-to-markdown==0.15
einops==0.8.1
email_validator==2.2.0
entrypoints==0.4
exceptiongroup==1.2.2
executing==2.1.0
fastapi==0.115.6
fastapi-cli==0.0.7
fastjsonschema==2.21.1
fastrlock==0.8.3
fe==0.3.33
ffmpy==0.4.0
filelock==3.17.0
flake8==7.1.1
flatbuffers==24.3.25
fonttools==4.55.3
fqdn==1.5.1
frozendict==2.4.2
frozenlist==1.5.0
fsspec==2024.2.0
google-api-core==2.24.1
google-auth==2.36.0
googleapis-common-protos==1.66.0
gpustat==1.1.1
gradio==3.38.0
gradio_client==1.5.1
grpcio==1.68.1
grpcio-channelz==1.34.0
h11==0.14.0
hf-xet==1.0.3
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.30.2
humanfriendly==10.0
idna==3.7
importlib_metadata==8.0.0
iniconfig==2.0.0
interegular==0.3.3
ipykernel==6.29.5
ipython==8.30.0
ipython-genutils==0.2.0
isoduration==20.11.0
isort==5.13.2
jedi==0.19.2
jedi-language-server==0.42.0
Jinja2==3.1.5
jinjasql==0.1.8
jiter==0.8.2
jmespath==0.10.0
joblib==1.4.2
json5==0.10.0
jsonpatch==1.33
jsonpointer==2.1
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
kiwisolver==1.4.7
kubemaker==0.2.17
kubernetes==9.0.0
lark==1.2.2
libmambapy==1.5.8
libro-core==0.1.6
libro-server==0.1.10
linkify-it-py==2.0.3
llguidance==0.7.19
llvmlite==0.44.0
lsprotocol==2023.0.1
lxml==5.3.0
markdown-it-py==2.2.0
matplotlib==3.9.3
matplotlib-inline==0.1.7
maya-tools==0.0.4
mccabe==0.7.0
mdit-py-plugins==0.3.3
mdurl==0.1.2
menuinst==2.2.0
mistral_common==1.5.4
mistune==0.8.4
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
multiprocess==0.70.16
narwhals==1.17.0
nbclient==0.5.13
nbconvert==6.4.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.2.1
ninja==********
notebook==6.4.6
numba==0.61.2
numpy==1.26.4
nvidia-cudnn-cu12==********
nvidia-cufile-cu12==********
nvidia-ml-py==12.570.86
oauthlib==3.2.2
openai==1.58.1
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python-headless==*********
opentelemetry-api==1.26.0
opentelemetry-exporter-otlp==1.26.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-exporter-otlp-proto-http==1.26.0
opentelemetry-proto==1.26.0
opentelemetry-sdk==1.26.0
opentelemetry-semantic-conventions==0.47b0
opentelemetry-semantic-conventions-ai==0.4.3
orjson==3.10.12
oss2==2.6.0
osscmd==0.4.5
outlines_core==0.1.26
overrides==7.7.0
packaging==24.2
pandas==2.0.1
pandocfilters==1.5.1
parso==0.8.4
partial-json-parser==*******.post5
peppercorn==0.6
pexpect==4.9.0
pillow==11.1.0
pip==24.2
platformdirs==3.11.0
pluggy==1.5.0
portalocker==3.0.0
prometheus_client==0.21.1
prometheus-fastapi-instrumentator==7.0.2
prompt_toolkit==3.0.48
propcache==0.2.1
proto-plus==1.26.0
protobuf==4.25.7
psutil==6.1.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyOpenSSL==24.3.0
py-cpuinfo==9.0.0
py-spy==0.4.0
pyaml==21.10.1
pyarrow==18.1.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pybind11==2.13.6
pycodestyle==2.12.1
pycosat==0.6.6
pycountry==24.6.1
pycparser==2.21
pycryptodome==3.21.0
pydantic==2.10.6
pydantic_core==2.27.2
pydocstyle==6.3.0
pydub==0.25.1
pyflakes==3.2.0
pygls==1.3.1
Pygments==2.18.0
pyhocon==0.3.61
pyinotify==0.9.6
pylint==3.3.2
pyodps==********
pyparsing==3.2.0
pytest==8.3.4
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==3.2.0
python-lsp-jsonrpc==1.1.2
python-lsp-server==1.12.0
python-multipart==0.0.19
python-prctl==1.8.1
pytoolconfig==1.3.1
pytz==2024.2
pyzmq==26.2.0
ray==2.43.0
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==14.0.0
rich-toolkit==0.14.3
rope==1.13.0
rpds-py==0.22.3
rsa==4.9
ruamel.yaml==0.18.6
ruamel.yaml.clib==0.2.8
ruff==0.8.2
ruff-lsp==0.0.59
safetensors==0.5.2
scipy==1.15.2
semantic-version==2.10.0
sentencepiece==0.2.0
setuptools==75.1.0
setuptools-scm==8.1.0
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.6
sqlparse==0.5.3
stack-data==0.6.3
starlette==0.41.3
tabulate==0.8.2
terminado==0.18.1
testpath==0.6.0
tiktoken==0.8.0
tinycss2==1.4.0
tokenizers==0.21.0
toml==0.10.2
tomli==2.2.1
tomlkit==0.13.2
torchaudio==2.6.0
tornado==6.4.2
tqdm==4.66.5
traitlets==5.14.3
transformers==4.51.3
trl==0.16.1
truststore==0.8.0
typer==0.15.2
types-python-dateutil==2.9.0.20241206
typing_extensions==4.12.2
tzdata==2024.2
uc-micro-py==1.0.3
ujson==5.10.0
uri-template==1.3.0
urllib3==2.2.3
urllib3==2.4.0
uvicorn==0.32.1
uvloop==0.21.0
virtualenv==20.21.0
watchfiles==1.0.4
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==11.0.3
wfbuilder==*********
wget==3.2
whatthepatch==1.0.7
wheel==0.44.0
wrapt==1.17.2
xgrammar==0.1.18
xxhash==3.5.0
yapf==0.43.0
yarl==1.18.3
zdfs-dfs==3.0.3
zipp==3.21.0
zstandard==0.23.0
antlr4-python3-runtime==4.9.3
nvidia-cusparse-cu12==**********
sentry-sdk==2.27.0
nvidia-cufft-cu12==*********
codetiming==1.4.0
scikit-learn==1.6.1
pyairports==2.1.1
nvidia-nvjitlink-cu12==12.6.85
blinker==1.9.0
outlines==0.0.46
pyserini==0.44.0
triton==3.0.0
Cython==3.0.12
wandb==0.19.10
GitPython==3.1.44
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
hydra-core==1.3.2
nvidia-cusparselt-cu12==0.6.3
torchvision==0.19.0
tensordict==0.7.2
threadpoolctl==3.6.0
nvidia-nccl-cu12==2.20.5
nvidia-cublas-cu12==********
MarkupSafe==3.0.2
lm-format-enforcer==0.10.6
nvidia-cuda-cupti-cu12==12.1.105
itsdangerous==2.2.0
nvidia-cusolver-cu12==**********
torch==2.4.0
nvidia-nvtx-cu12==12.1.105
nvidia-curand-cu12==**********
vllm==0.6.3
pyjnius==1.6.1
gitdb==4.0.12
sympy==1.14.0
faiss-gpu==1.7.2
onnxruntime==1.21.1
Werkzeug==3.1.3
Flask==3.1.0
xformers==0.0.27.post2
flash_attn==2.7.4.post1
omegaconf==2.3.0
setproctitle==1.3.5
docker-pycreds==0.4.0
gguf==0.10.0
smmap==5.0.2
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
importlib_resources==6.4.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.1
platformdirs==4.2.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.43.0
zipp==3.19.2
