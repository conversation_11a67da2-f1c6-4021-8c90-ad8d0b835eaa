{"os": "Linux-4.9.151-015.ali3000.alios7.x86_64-x86_64-with-glibc2.32", "python": "CPython 3.10.13", "startedAt": "2025-04-29T02:03:48.555548Z", "args": ["--node-ip-address=*************", "--node-manager-port=44791", "--object-store-name=/tmp/ray/session_2025-04-29_10-03-22_800382_269665/sockets/plasma_store", "--raylet-name=/tmp/ray/session_2025-04-29_10-03-22_800382_269665/sockets/raylet", "--redis-address=None", "--metrics-agent-port=60483", "--logging-rotate-bytes=536870912", "--logging-rotate-backup-count=5", "--runtime-env-agent-port=60280", "--gcs-address=*************:56894", "--session-name=session_2025-04-29_10-03-22_800382_269665", "--temp-dir=/tmp/ray", "--webui=127.0.0.1:8265", "--cluster-id=273089d45e6170892f0d424059db0143cd5c648bea2ddf8de028eacd", "--startup-token=11", "--worker-launch-time-ms=1745892206094", "--node-id=f6e5a604fa5489dcaf011921cff3d39d72fe7e9570e89bebeb36522b", "--runtime-env-hash=1830736042"], "program": "/opt/conda/lib/python3.10/site-packages/ray/_private/workers/default_worker.py", "git": {"remote": "*******************:dev_alg/Search-R1.git", "commit": "df7879ff7a0c16a50be8924c5f3bfc42b9b665e5"}, "root": "/ossfs/workspace/yuyichen.yyc/Search-R1", "host": "deveffinlptraingpu-55-033211213025", "executable": "/opt/conda/bin/python3", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 1, "disk": {"/": {"total": "260919263232", "used": "15500615680"}}, "memory": {"total": "53687091200"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85198045184", "architecture": "Ampere"}], "cudaVersion": "12.4"}