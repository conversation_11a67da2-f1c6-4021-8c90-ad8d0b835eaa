import sys

sys.path.append('../../../..')
from search_r1.search.codefuse import SearchFn


def test_schema():
    print(SearchFn.search_tool_schemas_str)


def test_semantic_search():
    res = SearchFn.do('SemanticCodeSearch', search_params={'query': '这个仓库是干啥的', 'extra_info': {'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 'branch': 'master'}})
    print(res)


def test_web_search():
    res = SearchFn.do('WebSearch', search_params={'query': 'pytorch官方文档'})
    print(res)


if __name__ == '__main__':
    test_semantic_search()
    test_web_search()
