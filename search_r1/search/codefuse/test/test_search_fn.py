import sys

sys.path.append('../../../..')
from search_r1.search.codefuse import SearchFn


def test_schema():
    print(SearchFn.search_tool_schemas_str)


def test_semantic_search():
    res = SearchFn.do('SemanticCodeSearch', search_params={'query': '这个仓库是干啥的', 'extra_info': {'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 'branch': 'master'}})
    print(res)


def test_web_search():
    res = SearchFn.do('WebSearch', search_params={'query': 'pytorch官方文档'})
    print(res)


def test_symbol_search():
    # 测试类搜索
    res = SearchFn.do('SymbolSearch', search_params={
        'class_name': 'ArrayList',
        'extra_info': {'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 'branch': 'master'}
    })
    print("Class search results:", res)

    # 测试方法搜索
    res = SearchFn.do('SymbolSearch', search_params={
        'class_name': 'ArrayList',
        'method_name': 'add',
        'extra_info': {'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 'branch': 'master'}
    })
    print("Method search results:", res)


def test_upstream_search():
    res = SearchFn.do('UpstreamSearch', search_params={
        'class_name': 'ArrayList',
        'method_name': 'add',
        'extra_info': {'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 'branch': 'master'}
    })
    print("Upstream search results:", res)


if __name__ == '__main__':
    print("=== Testing Schema ===")
    test_schema()

    print("\n=== Testing Semantic Search ===")
    test_semantic_search()

    print("\n=== Testing Symbol Search ===")
    test_symbol_search()

    print("\n=== Testing Upstream Search ===")
    test_upstream_search()

    print("\n=== Testing Web Search ===")
    test_web_search()
