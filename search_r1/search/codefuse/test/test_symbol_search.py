#!/usr/bin/env python3
"""
测试Symbol Search工具的脚本
"""

import sys
import os

# 移动到test目录运行
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../..'))
from search_r1.search.codefuse import SearchFn


def test_symbol_search_schema():
    """测试Schema是否正确注册"""
    print("=== Testing Symbol Search Schema ===")
    schemas = SearchFn.search_tool_schemas

    symbol_search_schema = None
    for schema in schemas:
        if schema['function']['name'] == 'SymbolSearch':
            symbol_search_schema = schema
            break

    if symbol_search_schema:
        print("✓ SymbolSearch schema found")
        print(f"Description: {symbol_search_schema['function']['description']}")
        print(f"Required params: {symbol_search_schema['function']['parameters']['required']}")

        # 检查参数定义
        props = symbol_search_schema['function']['parameters']['properties']
        print(f"Parameters: {list(props.keys())}")
        return True
    else:
        print("✗ SymbolSearch schema not found")
        return False


def test_symbol_search_config():
    """测试配置文件读取"""
    print("\n=== Testing Symbol Search Config ===")
    try:
        from search_r1.search.codefuse.symbol_search import SymbolSearch

        # 测试配置文件读取
        url = SymbolSearch.get_symbol_search_url(
            'https://code.alipay.com/checkstyle/checkstyle.git',
            'master'
        )
        print(f"✓ Config file read successfully")
        print(f"URL for checkstyle repo: {url}")
        return True

    except FileNotFoundError as e:
        print(f"✗ Config file not found: {e}")
        return False
    except Exception as e:
        print(f"✗ Config error: {e}")
        return False


def test_symbol_search_call():
    """测试Symbol Search API调用（预期会失败，因为没有实际服务）"""
    print("\n=== Testing Symbol Search API Call ===")
    try:
        # 测试类搜索
        print("Testing class search...")
        results = SearchFn.do('SymbolSearch', search_params={
            'class_name': 'TestClass',
            'method_name': '',
            'extra_info': {
                'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git',
                'branch': 'master'
            }
        })
        print(f"Class search results: {len(results)} items")

        # 测试方法搜索
        print("Testing method search...")
        results = SearchFn.do('SymbolSearch', search_params={
            'class_name': '',
            'method_name': 'testMethod',
            'extra_info': {
                'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git',
                'branch': 'master'
            }
        })
        print(f"Method search results: {len(results)} items")

        return True

    except Exception as e:
        print(f"✗ API call error: {e}")
        return False


def main():
    """主测试函数"""
    print("Symbol Search Tool Test Suite")
    print("=" * 50)

    tests = [
        test_symbol_search_schema,
        test_symbol_search_config,
        test_symbol_search_call
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")

    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. This is expected if Symbol Search server is not running.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
