class SearchFnException(Exception):
    pass


class SearchFnNotImplemented(SearchFnException):
    def __init__(self, msg="Search function not implemented"):
        self.msg = msg


class SearchFnParamMisSpecified(SearchFnException):
    def __init__(self, msg="Search function parameter mis-specified"):
        self.msg = msg


class SearchFnExecuteError(SearchFnException):
    def __init__(self, msg="Search function execute error"):
        self.msg = msg
