swe_prompt_template = """
Your task is to identify the key code snippets to resolve one or more issues within code repository {repo}. 
You are allowed to use the following search tools to access external knowledge within the code repository:
{search_tool_schemas}
You must conduct reasoning inside <think> and </think> first every time you get new information. 
After reasoning, if you find you lack some knowledge, you can call a search tool between <search> and </search>,
for example, <search> {{"search_tool": "SemanticCodeSearch", "search_params": {{"query": "your query here"}}}} </search>.
And it will return the top searched results between <information> and </information>, for example, <information> [{{"source": "codebase", "path": "path/to/the/code", "code": "code snippet here"}}, ...] </information>.
You can search as many times as your want. 
If you find no further external knowledge needed, you can directly provide the the key code snippets to resolve the issues as a list inside <answer> and </answer>, the code snippets should be from previous searched results.
For example, <answer> [{{"source": "codebase", "path": "path/to/the/code", "code": "code snippet here"}}, ...] </answer>.
The following are the titles of the issues: {issues}
"""
