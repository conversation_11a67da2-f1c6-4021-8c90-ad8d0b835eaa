import json
from typing import List, Any, Type, Dict
from abc import abstractmethod, <PERSON>
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from search_r1.utils.common import classproperty, is_abstract_class
from .exceptions import SearchFnNotImplemented, SearchFnParamMisSpecified


class SearchResult(ABC):
    @property
    def json(self) -> dict:
        raise NotImplementedError()

    @property
    def json_str(self) -> str:
        return json.dumps(self.json)


class SearchFn(ABC):
    description: str = ''
    enabled: bool = True
    max_search_reults: int = 2
    max_content_length: int = 2048

    @abstractmethod
    def __init__(self, **kwargs):
        raise NotImplementedError()

    @classproperty
    def name(cls) -> str:
        return cls.__name__

    @classproperty
    def required_params(cls) -> List[str]:
        raise NotImplementedError()

    @classmethod
    def check_params(cls, **params) -> bool:
        if all(param in params for param in cls.required_params):
            return True
        return False

    @classmethod
    def check_search_schema(cls, search_tool: str, search_params: dict) -> bool:
        if not search_tool in cls.functions:
            return True
        return cls.functions[search_tool].check_params(**search_params)

    @classproperty
    def functions(cls) -> Dict[str, Type['SearchFn']]:
        fn_map = {cls.__name__: cls}
        for f in cls.__subclasses__():
            fn_map.update(f.functions)

        fn_map = {
            f_name: f_type
            for f_name, f_type in fn_map.items()
            if not is_abstract_class(f_type) and f_type.enabled
        }

        return fn_map

    @classproperty
    def search_tool_schemas(cls) -> List[dict]:
        return [
            f_type.schema for f_name, f_type in cls.functions.items()
        ]

    @classproperty
    def search_tool_schemas_str(cls) -> str:
        return json.dumps(cls.search_tool_schemas, indent=4)

    @abstractmethod
    def execute(self) -> List[SearchResult]:
        raise NotImplementedError()

    @classmethod
    def do(cls, search_tool: str, search_params: dict) -> List[SearchResult]:
        if not search_tool in cls.functions:
            raise SearchFnNotImplemented('Search function not implemented: {}'.format(search_tool))
        print(f'executing {search_tool} with {search_params}')
        res = cls.functions[search_tool](**search_params).execute()
        return res

    @classmethod
    def batch_do(
            cls,
            search_tool_list: List[str],
            search_params_list: List[dict],
            max_workers: int = 32
    ) -> List[List[SearchResult]]:
        hint = 'search_tool_list and search_params_list must have the same length'
        assert len(search_tool_list) == len(search_params_list), hint

        res = []
        with ThreadPoolExecutor(min(len(search_tool_list), max_workers)) as executor:
            futures = [
                executor.submit(
                    cls.do,
                    search_tool=search_tool,
                    search_params=search_params,
                )
                for search_tool, search_params in zip(search_tool_list, search_params_list)
            ]
            for future in as_completed(futures):
                res.append(future.result())

        return res
