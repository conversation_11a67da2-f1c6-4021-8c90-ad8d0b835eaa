from typing import List
import traceback
from dataclasses import dataclass
import json
import requests
from .base import SearchFn, SearchResult
from .exceptions import SearchFnParamMisSpecified, SearchFnExecuteError
from ...utils.common import classproperty


@dataclass
class CodeChunk(SearchResult):
    id: str
    path: str
    code: str
    start_line: int
    end_line: int
    source: str = 'codebase'

    @property
    def json(self) -> dict:
        return {
            'source': self.source,
            'path': self.path,
            'code': self.code
        }


class SemanticCodeSearch(SearchFn):
    description = 'Semantic search within current codebase'
    enabled = True

    @classproperty
    def required_params(cls) -> List[str]:
        return ['query', 'extra_info']

    @classproperty
    def schema(cls) -> dict:
        return {
            "type": "function",
            "function": {
                "name": cls.name,
                "description": cls.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search query"
                        }
                    },
                    "required": ["query"]
                }
            }
        }

    @property
    def schema_str(cls) -> str:
        return json.dumps(cls.schema)

    def __init__(self, **kwargs):
        self.__class__.check_params(**kwargs)

        self.query = kwargs['query']
        self.extra_info = kwargs['extra_info']

    def execute(self) -> List[CodeChunk]:
        if not ('repo_url' in self.extra_info and 'branch' in self.extra_info):
            raise SearchFnParamMisSpecified(
                '`repo_url` and `branch` must be provided in extra_info for SemanticCodeSearch'
            )
        repo_info = {
            'repoURL': self.extra_info['repo_url'],
            'branch': self.extra_info['branch']
        }
        return self._execute(query=self.query, repo_info=repo_info)[:self.max_search_reults]

    def _execute(self, query: str, repo_info: dict) -> List[CodeChunk]:
        result = self.query_remote_code_index(query, repo_info)
        result = [
            CodeChunk(
                id='code_chunk_{}'.format(i),
                path=chunk['relativePath'],
                code=chunk['snippet'][:self.max_content_length],
                start_line=chunk['startLine'],
                end_line=chunk['endLine']
            )
            for i, chunk in enumerate(result)
        ]
        return result

    @staticmethod
    def query_remote_code_index(query: str, repo_info: dict) -> List[dict]:
        url = 'https://cfsearch.antgroup-inc.cn/openapi/search'
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'codefusesearch_token': 'b1eHTFGKRgOQmNI9C0tdYQ',
            'codefusesearch_user': 'test',
            'Cookie': 'BUSERVICE_SSO_V2=875EA9D63691EB9D059D139D595DB1625ADCBFA520F273B5B53526F24AF57E5CAC6AA4CBA34DE5422F4CBB4A25397BA9; spanner=XHRhCmyE2lP1cOM9tAp8w1ctd6TQmDy1'
        }

        payload = {
            "pipelineConfig": {
                "name": "augmentSearch",
                "description": "augmentSearch搜索",
                "indexCheck": True,
                "stages": [
                    {
                        "name": "RECALL",
                        "handlers": [
                            {
                                "name": "KeywordRecallHandler",
                                "config": {
                                    "queryTypeToSearch": "ORIGIN_QUERY",
                                    "queryTemplateName": "CHUNK_LEXCIAL_SEARCH_CHUNK_SIZE_512_CGE_2048",
                                    "limit": 10
                                }
                            },
                            {
                                "name": "UcsRecallHandler",
                                "config": {
                                    "queryTypeForEmbedding": "ORIGIN_QUERY",
                                    "queryTemplateName": "UCS_SEARCH",
                                    "limit": 10
                                }
                            }
                        ]
                    },
                    {
                        "name": "MERGE",
                        "handlers": [
                            {
                                "name": "CommonMergeHandler"
                            }
                        ]
                    },
                    {
                        "name": "POST_PROCESS",
                        "parallel": False,
                        "handlers": [
                            {
                                "name": "ChunkExtendHandler",
                                "config": {
                                    "tokenLimit": 90000000
                                }
                            }
                        ]
                    }
                ]
            },
            "query": query,
            "repoInfo": repo_info
        }
        response = requests.post(url, headers=headers, json=payload)

        try:
            response_text = json.loads(response.text)
            result_chunk = response_text['data']['codeChunkWithCompleteSchema']
            result_data = []
            for chunk in result_chunk:
                result_data.append(
                    {
                        "snippet": chunk.get('snippet', ''),
                        "relativePath": chunk.get('relativePath', ''),
                        "startLine": chunk.get('startLine', 0),
                        "endLine": chunk.get('endLine', 0)
                    }
                )

            return result_data
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            print('Failed to execute SemanticCodeSearch, returning [] instead.')
            # raise SearchFnExecuteError(f'Failed to execute SemanticCodeSearch: {e}')
            return []
