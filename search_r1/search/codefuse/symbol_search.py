from typing import List, Optional
import traceback
from dataclasses import dataclass
import json
import requests
from .base import SearchFn, SearchResult
from .exceptions import SearchFnParamMisSpecified, SearchFnExecuteError
from ...utils.common import classproperty


@dataclass
class SymbolResult(SearchResult):
    id: str
    path: str
    code: str
    start_line: int
    end_line: int
    class_name: Optional[str] = None
    method_name: Optional[str] = None
    symbol_type: str = 'symbol'  # 'class', 'method', 'function', etc.
    source: str = 'codebase'

    @property
    def json(self) -> dict:
        return {
            'source': self.source,
            'path': self.path,
            'code': self.code,
            'class_name': self.class_name,
            'method_name': self.method_name,
            'symbol_type': self.symbol_type
        }


@dataclass
class UpstreamResult(SearchResult):
    id: str
    path: str
    code: str
    start_line: int
    end_line: int
    caller_class: Optional[str] = None
    caller_method: Optional[str] = None
    target_class: Optional[str] = None
    target_method: Optional[str] = None
    source: str = 'codebase'

    @property
    def json(self) -> dict:
        return {
            'source': self.source,
            'path': self.path,
            'code': self.code,
            'caller_class': self.caller_class,
            'caller_method': self.caller_method,
            'target_class': self.target_class,
            'target_method': self.target_method
        }


class SymbolSearch(SearchFn):
    description = 'Search for class or method implementations by class name and method name'
    enabled = True

    @classproperty
    def required_params(cls) -> List[str]:
        return ['class_name', 'extra_info']

    @classproperty
    def schema(cls) -> dict:
        return {
            "type": "function",
            "function": {
                "name": cls.name,
                "description": cls.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "class_name": {
                            "type": "string",
                            "description": "The class name to search for"
                        },
                        "method_name": {
                            "type": "string",
                            "description": "The method name to search for (optional, if not provided, search for class definition)"
                        }
                    },
                    "required": ["class_name"]
                }
            }
        }

    @property
    def schema_str(cls) -> str:
        return json.dumps(cls.schema)

    def __init__(self, **kwargs):
        self.__class__.check_params(**kwargs)
        
        self.class_name = kwargs['class_name']
        self.method_name = kwargs.get('method_name', None)
        self.extra_info = kwargs['extra_info']

    def execute(self) -> List[SymbolResult]:
        if not ('repo_url' in self.extra_info and 'branch' in self.extra_info):
            raise SearchFnParamMisSpecified(
                '`repo_url` and `branch` must be provided in extra_info for SymbolSearch'
            )
        repo_info = {
            'repoURL': self.extra_info['repo_url'],
            'branch': self.extra_info['branch']
        }
        return self._execute(
            class_name=self.class_name, 
            method_name=self.method_name, 
            repo_info=repo_info
        )[:self.max_search_reults]

    def _execute(self, class_name: str, method_name: Optional[str], repo_info: dict) -> List[SymbolResult]:
        # 构建搜索查询
        if method_name:
            query = f"class {class_name} method {method_name}"
            search_type = "method"
        else:
            query = f"class {class_name}"
            search_type = "class"
            
        result = self.query_symbol_search(query, repo_info, search_type)
        result = [
            SymbolResult(
                id='symbol_{}'.format(i),
                path=chunk['relativePath'],
                code=chunk['snippet'][:self.max_content_length],
                start_line=chunk['startLine'],
                end_line=chunk['endLine'],
                class_name=class_name,
                method_name=method_name,
                symbol_type=search_type
            )
            for i, chunk in enumerate(result)
        ]
        return result

    @staticmethod
    def query_symbol_search(query: str, repo_info: dict, search_type: str) -> List[dict]:
        url = 'https://cfsearch.antgroup-inc.cn/openapi/search'
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'codefusesearch_token': 'b1eHTFGKRgOQmNI9C0tdYQ',
            'codefusesearch_user': 'test',
            'Cookie': 'BUSERVICE_SSO_V2=875EA9D63691EB9D059D139D595DB1625ADCBFA520F273B5B53526F24AF57E5CAC6AA4CBA34DE5422F4CBB4A25397BA9; spanner=XHRhCmyE2lP1cOM9tAp8w1ctd6TQmDy1'
        }

        # 针对符号搜索优化的管道配置
        payload = {
            "pipelineConfig": {
                "name": "symbolSearch",
                "description": "Symbol search for classes and methods",
                "indexCheck": True,
                "stages": [
                    {
                        "name": "RECALL",
                        "handlers": [
                            {
                                "name": "KeywordRecallHandler",
                                "config": {
                                    "queryTypeToSearch": "ORIGIN_QUERY",
                                    "queryTemplateName": "SYMBOL_SEARCH_TEMPLATE",
                                    "limit": 15  # 增加限制以获取更多可能的重名符号
                                }
                            },
                            {
                                "name": "UcsRecallHandler",
                                "config": {
                                    "queryTypeForEmbedding": "ORIGIN_QUERY",
                                    "queryTemplateName": "SYMBOL_UCS_SEARCH",
                                    "limit": 15
                                }
                            }
                        ]
                    },
                    {
                        "name": "MERGE",
                        "handlers": [
                            {
                                "name": "CommonMergeHandler"
                            }
                        ]
                    },
                    {
                        "name": "POST_PROCESS",
                        "parallel": False,
                        "handlers": [
                            {
                                "name": "ChunkExtendHandler",
                                "config": {
                                    "tokenLimit": 90000000
                                }
                            }
                        ]
                    }
                ]
            },
            "query": query,
            "repoInfo": repo_info,
            "searchType": search_type  # 添加搜索类型信息
        }
        response = requests.post(url, headers=headers, json=payload)

        try:
            response_text = json.loads(response.text)
            result_chunk = response_text['data']['codeChunkWithCompleteSchema']
            result_data = []
            for chunk in result_chunk:
                result_data.append(
                    {
                        "snippet": chunk.get('snippet', ''),
                        "relativePath": chunk.get('relativePath', ''),
                        "startLine": chunk.get('startLine', 0),
                        "endLine": chunk.get('endLine', 0)
                    }
                )

            return result_data
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            print('Failed to execute SymbolSearch, returning [] instead.')
            return []


class UpstreamSearch(SearchFn):
    description = 'Search for code that calls a specific class method (upstream callers)'
    enabled = True

    @classproperty
    def required_params(cls) -> List[str]:
        return ['class_name', 'method_name', 'extra_info']

    @classproperty
    def schema(cls) -> dict:
        return {
            "type": "function",
            "function": {
                "name": cls.name,
                "description": cls.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "class_name": {
                            "type": "string",
                            "description": "The target class name whose method is being called"
                        },
                        "method_name": {
                            "type": "string",
                            "description": "The target method name being called"
                        }
                    },
                    "required": ["class_name", "method_name"]
                }
            }
        }

    @property
    def schema_str(cls) -> str:
        return json.dumps(cls.schema)

    def __init__(self, **kwargs):
        self.__class__.check_params(**kwargs)
        
        self.class_name = kwargs['class_name']
        self.method_name = kwargs['method_name']
        self.extra_info = kwargs['extra_info']

    def execute(self) -> List[UpstreamResult]:
        if not ('repo_url' in self.extra_info and 'branch' in self.extra_info):
            raise SearchFnParamMisSpecified(
                '`repo_url` and `branch` must be provided in extra_info for UpstreamSearch'
            )
        repo_info = {
            'repoURL': self.extra_info['repo_url'],
            'branch': self.extra_info['branch']
        }
        return self._execute(
            class_name=self.class_name, 
            method_name=self.method_name, 
            repo_info=repo_info
        )[:self.max_search_reults]

    def _execute(self, class_name: str, method_name: str, repo_info: dict) -> List[UpstreamResult]:
        # 构建上游调用搜索查询
        query = f"{class_name}.{method_name}() call usage invocation"
        
        result = self.query_upstream_search(query, repo_info, class_name, method_name)
        result = [
            UpstreamResult(
                id='upstream_{}'.format(i),
                path=chunk['relativePath'],
                code=chunk['snippet'][:self.max_content_length],
                start_line=chunk['startLine'],
                end_line=chunk['endLine'],
                target_class=class_name,
                target_method=method_name,
                # 这些字段可以通过进一步的代码分析来填充
                caller_class=chunk.get('callerClass', None),
                caller_method=chunk.get('callerMethod', None)
            )
            for i, chunk in enumerate(result)
        ]
        return result

    @staticmethod
    def query_upstream_search(query: str, repo_info: dict, class_name: str, method_name: str) -> List[dict]:
        url = 'https://cfsearch.antgroup-inc.cn/openapi/search'
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'codefusesearch_token': 'b1eHTFGKRgOQmNI9C0tdYQ',
            'codefusesearch_user': 'test',
            'Cookie': 'BUSERVICE_SSO_V2=875EA9D63691EB9D059D139D595DB1625ADCBFA520F273B5B53526F24AF57E5CAC6AA4CBA34DE5422F4CBB4A25397BA9; spanner=XHRhCmyE2lP1cOM9tAp8w1ctd6TQmDy1'
        }

        # 针对上游调用搜索优化的管道配置
        payload = {
            "pipelineConfig": {
                "name": "upstreamSearch",
                "description": "Upstream caller search for method invocations",
                "indexCheck": True,
                "stages": [
                    {
                        "name": "RECALL",
                        "handlers": [
                            {
                                "name": "KeywordRecallHandler",
                                "config": {
                                    "queryTypeToSearch": "ORIGIN_QUERY",
                                    "queryTemplateName": "UPSTREAM_SEARCH_TEMPLATE",
                                    "limit": 20  # 增加限制以获取更多调用点
                                }
                            },
                            {
                                "name": "UcsRecallHandler",
                                "config": {
                                    "queryTypeForEmbedding": "ORIGIN_QUERY",
                                    "queryTemplateName": "UPSTREAM_UCS_SEARCH",
                                    "limit": 20
                                }
                            }
                        ]
                    },
                    {
                        "name": "MERGE",
                        "handlers": [
                            {
                                "name": "CommonMergeHandler"
                            }
                        ]
                    },
                    {
                        "name": "POST_PROCESS",
                        "parallel": False,
                        "handlers": [
                            {
                                "name": "ChunkExtendHandler",
                                "config": {
                                    "tokenLimit": 90000000
                                }
                            }
                        ]
                    }
                ]
            },
            "query": query,
            "repoInfo": repo_info,
            "searchType": "upstream",
            "targetClass": class_name,
            "targetMethod": method_name
        }
        response = requests.post(url, headers=headers, json=payload)

        try:
            response_text = json.loads(response.text)
            result_chunk = response_text['data']['codeChunkWithCompleteSchema']
            result_data = []
            for chunk in result_chunk:
                result_data.append(
                    {
                        "snippet": chunk.get('snippet', ''),
                        "relativePath": chunk.get('relativePath', ''),
                        "startLine": chunk.get('startLine', 0),
                        "endLine": chunk.get('endLine', 0),
                        "callerClass": chunk.get('callerClass', None),
                        "callerMethod": chunk.get('callerMethod', None)
                    }
                )

            return result_data
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            print('Failed to execute UpstreamSearch, returning [] instead.')
            return []
