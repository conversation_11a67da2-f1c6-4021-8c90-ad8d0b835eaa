from typing import List
import traceback
from dataclasses import dataclass
import json
import requests
import os
from .base import SearchFn, SearchResult
from .exceptions import SearchFnParamMisSpecified, SearchFnExecuteError
from ...utils.common import classproperty


@dataclass
class SymbolResult(SearchResult):
    id: str
    path: str
    code: str
    start_line: int
    end_line: int
    class_name: str
    method_name: str
    source: str = 'symbol'

    @property
    def json(self) -> dict:
        return {
            'source': self.source,
            'path': self.path,
            'code': self.code,
            'class_name': self.class_name,
            'method_name': self.method_name,
        }

class SymbolSearch(SearchFn):
    description = 'Search for class or method implementations by class name and method name'
    enabled = True

    @classproperty
    def required_params(cls) -> List[str]:
        return ['class_name', 'method_name', 'extra_info']

    @classproperty
    def schema(cls) -> dict:
        return {
            "type": "function",
            "function": {
                "name": cls.name,
                "description": cls.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "class_name": {
                            "type": "string",
                            "description": "The class name to search for (can be empty string)"
                        },
                        "method_name": {
                            "type": "string",
                            "description": "The method name to search for (can be empty string)"
                        }
                    },
                    "required": ["class_name", "method_name"]
                }
            }
        }

    @property
    def schema_str(cls) -> str:
        return json.dumps(cls.schema)

    def __init__(self, **kwargs):
        self.__class__.check_params(**kwargs)

        self.class_name = kwargs['class_name']
        self.method_name = kwargs['method_name']
        self.extra_info = kwargs['extra_info']

    def execute(self) -> List[SymbolResult]:
        if not ('repo_url' in self.extra_info and 'branch' in self.extra_info):
            raise SearchFnParamMisSpecified(
                '`repo_url` and `branch` must be provided in extra_info for SymbolSearch'
            )
        repo_info = {
            'repoURL': self.extra_info['repo_url'],
            'branch': self.extra_info['branch']
        }
        return self._execute(
            class_name=self.class_name,
            method_name=self.method_name,
            repo_info=repo_info
        )[:self.max_search_reults]

    def _execute(self, class_name: str, method_name: str, repo_info: dict) -> List[SymbolResult]:
        # 直接调用symbol search API
        result = self.query_symbol_search(class_name, method_name, repo_info)

        # 转换为SymbolResult对象
        symbol_results = [
            SymbolResult(
                id='symbol_{}'.format(i),
                path=chunk['relativePath'],
                code=chunk['snippet'][:self.max_content_length],
                start_line=chunk['startLine'],
                end_line=chunk['endLine'],
                class_name=chunk.get('className', class_name),
                method_name=chunk.get('methodName', method_name),
            )
            for i, chunk in enumerate(result)
        ]
        return symbol_results

    @staticmethod
    def get_symbol_search_url(repo_url: str, branch: str) -> str:
        """
        根据repo_url和branch从配置文件中获取对应的symbol search URL
        """
        # 配置文件路径，你可以根据需要修改这个路径
        config_file_path = os.path.join(os.path.dirname(__file__), 'search_config.json')

        try:
            with open(config_file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 构建查找键，可以根据你的需求调整格式
            repo_key = f"{repo_url}#{branch}"

            # 如果找到精确匹配
            if repo_key in config:
                return config[repo_key]

            raise KeyError(f"No symbol search URL found for {repo_url}#{branch}")

        except FileNotFoundError:
            print(f"Symbol search config file not found: {config_file_path}")
            print("Please create the config file with the following format:")
            print('{')
            print('  "https://code.alipay.com/checkstyle/checkstyle.git#master": "http://your-symbol-search-url",')
            print('  "default": "http://default-symbol-search-url"')
            print('}')
            raise
        except Exception as e:
            print(f"Error reading symbol search config: {e}")
            raise

    @staticmethod
    def query_symbol_search(class_name: str, method_name: str, repo_info: dict) -> List[dict]:
        """
        调用symbol search API
        """
        try:
            # 获取symbol search URL
            symbol_search_url = SymbolSearch.get_symbol_search_url(
                repo_info['repoURL'],
                repo_info['branch']
            )
            symbol_search_url+='/findMethod'

            headers = {'Content-Type': 'application/json'}
            data = {
                "className": class_name if class_name else "",
                "methodName": method_name if method_name else ""
            }

            print(f"Calling symbol search API: {symbol_search_url}")
            print(f"Request data: {json.dumps(data, ensure_ascii=False)}")

            response = requests.post(symbol_search_url, headers=headers, json=data)

            if response.status_code != 200:
                print(f"Symbol search API error: {response.status_code}, {response.text}")
                return []

            result = response.json()
            #print(f"Symbol search raw result: {json.dumps(result, ensure_ascii=False)}")

            # 处理返回结果
            data_list = []
            for class_info in result:
                if class_name != "" and (method_name == "" or method_name is None):
                    # 搜索类定义
                    data_list.append({
                        "relativePath": class_info["filePath"],
                        "className": class_info["className"],
                        "snippet": class_info["body"],
                        "startLine": class_info["startLine"],
                        "endLine": class_info["endLine"]
                    })
                elif method_name != "" and method_name is not None:
                    # 搜索方法定义
                    for method in class_info.get("methods", []):
                        data_list.append({
                            "relativePath": class_info["filePath"],
                            "className": class_info["className"],
                            "snippet": method["body"],
                            "startLine": method["startLine"],
                            "endLine": method["endLine"]
                        })
                else:
                    print("ERROR: Invalid search parameters!")

            #print(f"Processed symbol search result: {json.dumps(data_list, ensure_ascii=False)}")
            return data_list

        except Exception as e:
            print(f"Symbol search error: {e}")
            print(traceback.format_exc())
            print('Failed to execute SymbolSearch, returning [] instead.')
            return []
