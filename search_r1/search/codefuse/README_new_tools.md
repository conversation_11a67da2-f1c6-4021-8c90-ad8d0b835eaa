# 新增检索工具说明

本文档介绍了新增的两个检索工具：`SymbolSearch` 和 `UpstreamSearch`。

## SymbolSearch - 符号搜索工具

### 功能描述
根据用户提供的类名和方法名，搜索对应的类或方法的实现代码。由于可能存在重名情况，可能返回多段代码。

### 参数说明
- `class_name` (必需): 要搜索的类名
- `method_name` (可选): 要搜索的方法名，如果不提供则搜索类定义
- `extra_info` (必需): 包含 `repo_url` 和 `branch` 的额外信息

### Function Call Schema
```json
{
    "type": "function",
    "function": {
        "name": "SymbolSearch",
        "description": "Search for class or method implementations by class name and method name",
        "parameters": {
            "type": "object",
            "properties": {
                "class_name": {
                    "type": "string",
                    "description": "The class name to search for"
                },
                "method_name": {
                    "type": "string",
                    "description": "The method name to search for (optional, if not provided, search for class definition)"
                }
            },
            "required": ["class_name"]
        }
    }
}
```

### 使用示例

#### 搜索类定义
```python
from search_r1.search.codefuse import SearchFn

# 搜索 CheckstyleException 类的定义
results = SearchFn.do('SymbolSearch', search_params={
    'class_name': 'CheckstyleException',
    'extra_info': {
        'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 
        'branch': 'master'
    }
})

for result in results:
    print(f"Found in: {result.path}")
    print(f"Lines: {result.start_line}-{result.end_line}")
    print(f"Code: {result.code[:200]}...")
```

#### 搜索方法实现
```python
# 搜索 CheckstyleException 类中的 getMessage 方法
results = SearchFn.do('SymbolSearch', search_params={
    'class_name': 'CheckstyleException',
    'method_name': 'getMessage',
    'extra_info': {
        'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 
        'branch': 'master'
    }
})
```

#### 在提示模版中使用
```xml
<search> {"search_tool": "SymbolSearch", "search_params": {"class_name": "ArrayList"}} </search>
<search> {"search_tool": "SymbolSearch", "search_params": {"class_name": "ArrayList", "method_name": "add"}} </search>
```

## UpstreamSearch - 上游调用搜索工具

### 功能描述
根据用户提供的类名和方法名，搜索调用了该方法的代码（上游调用者）。同样可能返回多段代码。

### 参数说明
- `class_name` (必需): 目标类名，其方法被调用
- `method_name` (必需): 目标方法名，被调用的方法
- `extra_info` (必需): 包含 `repo_url` 和 `branch` 的额外信息

### Function Call Schema
```json
{
    "type": "function",
    "function": {
        "name": "UpstreamSearch",
        "description": "Search for code that calls a specific class method (upstream callers)",
        "parameters": {
            "type": "object",
            "properties": {
                "class_name": {
                    "type": "string",
                    "description": "The target class name whose method is being called"
                },
                "method_name": {
                    "type": "string",
                    "description": "The target method name being called"
                }
            },
            "required": ["class_name", "method_name"]
        }
    }
}
```

### 使用示例

#### 搜索方法调用者
```python
from search_r1.search.codefuse import SearchFn

# 搜索调用了 CheckstyleException.getMessage() 的代码
results = SearchFn.do('UpstreamSearch', search_params={
    'class_name': 'CheckstyleException',
    'method_name': 'getMessage',
    'extra_info': {
        'repo_url': 'https://code.alipay.com/checkstyle/checkstyle.git', 
        'branch': 'master'
    }
})

for result in results:
    print(f"Caller found in: {result.path}")
    print(f"Lines: {result.start_line}-{result.end_line}")
    print(f"Target: {result.target_class}.{result.target_method}")
    print(f"Code: {result.code[:200]}...")
```

#### 在提示模版中使用
```xml
<search> {"search_tool": "UpstreamSearch", "search_params": {"class_name": "ArrayList", "method_name": "add"}} </search>
```

## 返回结果格式

### SymbolResult
```python
@dataclass
class SymbolResult(SearchResult):
    id: str                    # 结果唯一标识
    path: str                  # 文件路径
    code: str                  # 代码内容
    start_line: int            # 起始行号
    end_line: int              # 结束行号
    class_name: Optional[str]  # 类名
    method_name: Optional[str] # 方法名
    symbol_type: str           # 符号类型 ('class' 或 'method')
    source: str = 'codebase'   # 数据源
```

### UpstreamResult
```python
@dataclass
class UpstreamResult(SearchResult):
    id: str                      # 结果唯一标识
    path: str                    # 文件路径
    code: str                    # 代码内容
    start_line: int              # 起始行号
    end_line: int                # 结束行号
    caller_class: Optional[str]  # 调用者类名
    caller_method: Optional[str] # 调用者方法名
    target_class: Optional[str]  # 目标类名
    target_method: Optional[str] # 目标方法名
    source: str = 'codebase'     # 数据源
```

## 注意事项

1. **自动注册**: 这两个工具会自动注册到 `SearchFn` 系统中，无需手动配置。

2. **错误处理**: 如果API调用失败，工具会返回空列表而不是抛出异常，确保系统稳定性。

3. **结果限制**: 默认最多返回2个结果，可以通过修改 `max_search_reults` 属性来调整。

4. **内容长度**: 代码内容默认最大长度为2048字符，可以通过修改 `max_content_length` 属性来调整。

5. **查询优化**: 
   - SymbolSearch 使用 "class {class_name} method {method_name}" 格式的查询
   - UpstreamSearch 使用 "{class_name}.{method_name}() call usage invocation" 格式的查询

## 测试

运行测试文件来验证工具功能：

```bash
cd search_r1/search/codefuse/test
PYTHONPATH=../../../.. python3 test_search_fn.py
```

这将测试所有搜索工具，包括新增的 SymbolSearch 和 UpstreamSearch。
