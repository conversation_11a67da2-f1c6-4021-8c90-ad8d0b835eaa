from typing import List
import requests
from dataclasses import dataclass
import json
# from layotto import AntLayottoClient
# from evaluation_for_ragflow.oneapi.antragflow.RagLlmSearchFacade import (
#     RagLlmSearchFacade,
#     RagSearchRequest,
# )
from .base import SearchFn, SearchResult
from .exceptions import SearchFnExecuteError
from ...utils.common import classproperty


# client = AntLayottoClient(
#     max_send_message_length=100 * 1024 * 1024,
#     max_receive_message_length=100 * 1024 * 1024
# )
# client.initialize_app("search-r1")
#
# client.mosn_client.subscribe(RagLlmSearchFacade.ID)
# basementurl = RagLlmSearchFacade(client.layotto_client)


@dataclass
class WebPage(SearchResult):
    url: str
    title: str
    abstract: str
    content: int
    source: str = 'web'

    @property
    def json(self) -> dict:
        return {
            'source': self.source,
            'title': self.title,
            'content': self.content,
        }


class WebSearch(SearchFn):
    description = "Search the web for external knowledge"
    enabled = False

    @classproperty
    def required_params(cls) -> List[str]:
        return ['query', 'extra_info']

    @classproperty
    def schema(cls) -> dict:
        return {
            "type": "function",
            "function": {
                "name": cls.name,
                "description": cls.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search query"
                        }
                    },
                    "required": ["query"]
                }
            }
        }
    
    @property
    def schema_str(cls) -> str:
        return json.dumps(cls.schema)

    def __init__(self, **kwargs):
        self.__class__.check_params(**kwargs)
        self.query = kwargs['query']

    def execute(self) -> List[WebPage]:
        return self._execute()[:self.max_search_reults]

    def _execute(self) -> List[WebPage]:
        web_res: List[dict] = self.search_web(self.query)
        res: List[WebPage] = [
            WebPage(
                url=wr["url"],
                title=wr["title"],
                abstract=wr["searchDocAbstract"]["abstractOnline"],
                content=wr["doc"][:self.max_content_length]
            ) for wr in web_res
        ]
        return res

    def search_web(self, query: str) -> List[dict]:
        url = 'https://cfsearch.antgroup-inc.cn/openapi/web/search'
        headers = {
            'Content-Type': 'application/json',
            'Cookie': 'spanner=HwZuyM1Dv9h8SsmdvjzbHEZlrabibsygXt2T4qEYgj0=',
            'codefusesearch_user': 'test',
            'codefusesearch_token': 'b1eHTFGKRgOQmNI9C0tdYQ'
        }
        resp = requests.post(url, headers=headers, data=json.dumps({'query': query, 'size': self.max_search_reults}))

        try:
            res = resp.json()
            assert res['errorType'] == 'SUCCESS', 'search web error'
            return res['data']
        except Exception as e:
            print(f'search web error: {e}, with query: {query}')
            print(resp.text)
            print('Failed to execute WebSearch, returning [] instead.')
            # raise SearchFnExecuteError(f'search web error: {e}')
            return []
