from typing import List
import traceback
from dataclasses import dataclass
import json
import requests
import os
from .base import SearchFn, SearchResult
from .exceptions import SearchFnParamMisSpecified, SearchFnExecuteError
from ...utils.common import classproperty


@dataclass
class UpstreamResult(SearchResult):
    id: str
    path: str
    code: str
    start_line: int
    end_line: int
    caller_class: str
    caller_method: str
    target_class: str
    target_method: str
    source: str = 'upstream'

    @property
    def json(self) -> dict:
        return {
            'source': self.source,
            'path': self.path,
            'code': self.code,
            'caller_class': self.caller_class,
            'caller_method': self.caller_method,
            'target_class': self.target_class,
            'target_method': self.target_method
        }


class UpstreamSearch(SearchFn):
    description = 'Search for code that calls a specific class method (upstream callers)'
    enabled = True

    @classproperty
    def required_params(cls) -> List[str]:
        return ['class_name', 'method_name', 'extra_info']

    @classproperty
    def schema(cls) -> dict:
        return {
            "type": "function",
            "function": {
                "name": cls.name,
                "description": cls.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "class_name": {
                            "type": "string",
                            "description": "The target class name whose method is being called (can be empty string)"
                        },
                        "method_name": {
                            "type": "string",
                            "description": "The target method name being called (must be non-empty)"
                        }
                    },
                    "required": ["class_name", "method_name"]
                }
            }
        }

    @property
    def schema_str(cls) -> str:
        return json.dumps(cls.schema)

    def __init__(self, **kwargs):
        self.__class__.check_params(**kwargs)

        self.class_name = kwargs['class_name']
        self.method_name = kwargs['method_name']
        self.extra_info = kwargs['extra_info']

    def execute(self) -> List[UpstreamResult]:
        if not ('repo_url' in self.extra_info and 'branch' in self.extra_info):
            raise SearchFnParamMisSpecified(
                '`repo_url` and `branch` must be provided in extra_info for UpstreamSearch'
            )
        repo_info = {
            'repoURL': self.extra_info['repo_url'],
            'branch': self.extra_info['branch']
        }
        return self._execute(
            class_name=self.class_name,
            method_name=self.method_name,
            repo_info=repo_info
        )[:self.max_search_reults]

    def _execute(self, class_name: str, method_name: str, repo_info: dict) -> List[UpstreamResult]:
        # 直接调用upstream search API
        result = self.query_upstream_search(class_name, method_name, repo_info)

        # 转换为UpstreamResult对象
        upstream_results = [
            UpstreamResult(
                id='upstream_{}'.format(i),
                path=chunk['relativePath'],
                code=chunk['snippet'][:self.max_content_length],
                start_line=chunk['startLine'],
                end_line=chunk['endLine'],
                target_class=class_name,
                target_method=method_name,
                caller_class=chunk['className'],
                caller_method=chunk['methodName']
            )
            for i, chunk in enumerate(result)
        ]
        return upstream_results

    @staticmethod
    def get_upstream_search_url(repo_url: str, branch: str) -> str:
        """
        根据repo_url和branch从配置文件中获取对应的upstream search URL
        """
        # 配置文件路径
        config_file_path = os.path.join(os.path.dirname(__file__), 'search_config.json')

        try:
            with open(config_file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 构建查找键
            repo_key = f"{repo_url}#{branch}"

            # 如果找到精确匹配
            if repo_key in config:
                return config[repo_key]

            raise KeyError(f"No upstream search URL found for {repo_url}#{branch}")

        except FileNotFoundError:
            print(f"Upstream search config file not found: {config_file_path}")
            print("Please create the config file with the following format:")
            print('{')
            print('  "https://code.alipay.com/checkstyle/checkstyle.git#master": "http://your-upstream-search-url",')
            print('  "default": "http://default-upstream-search-url"')
            print('}')
            raise
        except Exception as e:
            print(f"Error reading upstream search config: {e}")
            raise

    @staticmethod
    def query_upstream_search(class_name: str, method_name: str, repo_info: dict) -> List[dict]:
        """
        调用upstream search API
        """
        try:
            # 获取upstream search URL
            upstream_search_url = UpstreamSearch.get_upstream_search_url(
                repo_info['repoURL'],
                repo_info['branch']
            )
            upstream_search_url += '/findUsages'

            headers = {'Content-Type': 'application/json'}
            data = {
                "className": class_name if class_name else "",
                "methodName": method_name if method_name else ""
            }

            print(f"Calling upstream search API: {upstream_search_url}")
            print(f"Request data: {json.dumps(data, ensure_ascii=False)}")

            response = requests.post(upstream_search_url, headers=headers, json=data)

            if response.status_code != 200:
                print(f"Upstream search API error: {response.status_code}, {response.text}")
                return []

            result = response.json()
            #print(f"Upstream search raw result: {json.dumps(result, ensure_ascii=False)}")

            # 处理返回结果，按照你提供的格式
            data_list = []
            for called_class in result:
                for class_info in called_class.get("methodReferenceList", []):
                    for method in class_info.get("referencedOfMethodList", []):
                        data_list.append({
                            "relativePath": class_info["filePath"],
                            "className": class_info.get("className", []),
                            "methodName": method["methodName"],
                            "snippet": method["text"],
                            "startLine": method["startLine"],
                            "endLine": method["endLine"]
                        })

            #print(f"Processed upstream search result: {json.dumps(data_list, ensure_ascii=False)}")
            return data_list

        except Exception as e:
            print(f"Upstream search error: {e}")
            print(traceback.format_exc())
            print('Failed to execute UpstreamSearch, returning [] instead.')
            return []
