import jsonlines
import sys
sys.path.append('..')

from search_r1.search.codefuse import SearchFn


def trigger_indexing(repo_url: str, branch: str) -> None:
    try:
        query = '这个仓库是干啥的'
        SearchFn.do('SemanticCodeSearch', search_params={'query': query,  'extra_info': {'repo_url': repo_url, 'branch': branch}})
    except:
        pass

    return


if __name__ == '__main__':
    data_path = '../data/processed_output.jsonl'

    all_repo = set()
    with jsonlines.open(data_path) as reader:
        for obj in reader:
            repo_url = 'https://code.alipay.com/{}/{}.git'.format(obj['org'], obj['repo'])
            branch = obj['ref']
            all_repo.add((repo_url, branch))

    print(all_repo)
    
    for repo_url, branch in all_repo:
        trigger_indexing(repo_url, branch)
