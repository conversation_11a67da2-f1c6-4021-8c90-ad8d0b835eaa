import json
import random
import re
import jsonlines
import pandas as pd
import sys

sys.path.append('..')
from search_r1.search.codefuse.prompt_template import swe_prompt_template
from search_r1.search.codefuse import SearchFn


def process_swe_data(
        input_path: str,
        train_path: str,
        test_path: str,
        train_test_split: float = 0.9
) -> None:
    with jsonlines.open(input_path, 'r') as reader:
        data = [obj for obj in reader]

    random.shuffle(data)

    df = pd.DataFrame(data)
    print(df.columns)

    df['data_source'] = 'code'
    df['issues'] = df.apply(
        lambda x: re.findall(r'\[Issue \d+: .+?\]', x['question']),
        axis=1
    )
    df = df[df['issues'].apply(len) > 0]
    df['issues'] = df.apply(
        lambda x: x['issues'][0], axis=1
    )

    df = df[df['golden_answers'].apply(len) > 0]

    df['prompt'] = df.apply(
        lambda x: swe_prompt_template.format(
            repo=f"{x['org']}/{x['repo']}",
            search_tool_schemas=SearchFn.search_tool_schemas_str,
            issues=x['issues'],
        ),
        axis=1
    )
    df['prompt'] = df.apply(
        lambda x: [
            {
                'role': 'user',
                'content': x['prompt']
            }
        ],
        axis=1
    )

    df['ind'] = df.index
    df['repo_url'] = df.apply(
        lambda x: 'https://code.alipay.com/{}/{}.git'.format(x['org'], x['repo']),
        axis=1
    )
    df['extra_info'] = df.apply(
        lambda x: {
            'index': x['ind'],
            'repo_url': x['repo_url'],
            'branch': x['ref']
        },
        axis=1
    )

    df['reward_model'] = df.apply(
        lambda x: {
            'style': 'rule',
            'ground_truth': {'target': x['golden_answers']},
        },
        axis=1
    )

    df = df[
        ['prompt', 'reward_model', 'extra_info', 'golden_answers', 'data_source']
    ]

    split = int(len(df) * train_test_split)
    df[:split].to_parquet(train_path, index=False)
    df[split:].to_parquet(test_path, index=False)
    df[split:].to_csv(test_path.replace('.parquet', '.csv'), index=False)

    return


if __name__ == '__main__':
    input_path = '../data/processed_output.jsonl'
    train_path = '../data/swe_train.parquet'
    test_path = '../data/swe_test.parquet'
    process_swe_data(input_path, train_path, test_path)
