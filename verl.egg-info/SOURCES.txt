LICENSE
README.md
pyproject.toml
setup.py
./search_r1/__init__.py
./search_r1/llm_agent/__init__.py
./search_r1/llm_agent/generation.py
./search_r1/llm_agent/tensor_helper.py
./verl/__init__.py
./verl/protocol.py
./verl/models/__init__.py
./verl/models/registry.py
./verl/models/weight_loader_registry.py
./verl/models/llama/__init__.py
./verl/models/llama/megatron/__init__.py
./verl/models/llama/megatron/modeling_llama_megatron.py
./verl/models/llama/megatron/checkpoint_utils/__init__.py
./verl/models/llama/megatron/checkpoint_utils/llama_loader.py
./verl/models/llama/megatron/checkpoint_utils/llama_saver.py
./verl/models/llama/megatron/layers/__init__.py
./verl/models/llama/megatron/layers/parallel_attention.py
./verl/models/llama/megatron/layers/parallel_decoder.py
./verl/models/llama/megatron/layers/parallel_linear.py
./verl/models/llama/megatron/layers/parallel_mlp.py
./verl/models/llama/megatron/layers/parallel_rmsnorm.py
./verl/models/transformers/__init__.py
./verl/models/transformers/llama.py
./verl/models/transformers/monkey_patch.py
./verl/models/transformers/qwen2.py
./verl/single_controller/__init__.py
./verl/single_controller/base/__init__.py
./verl/single_controller/base/decorator.py
./verl/single_controller/base/worker.py
./verl/single_controller/base/worker_group.py
./verl/single_controller/base/megatron/__init__.py
./verl/single_controller/base/megatron/worker.py
./verl/single_controller/base/megatron/worker_group.py
./verl/single_controller/base/register_center/__init__.py
./verl/single_controller/base/register_center/ray.py
./verl/single_controller/ray/__init__.py
./verl/single_controller/ray/base.py
./verl/single_controller/ray/megatron.py
./verl/third_party/__init__.py
./verl/third_party/vllm/__init__.py
./verl/third_party/vllm/vllm_v_0_3_1/__init__.py
./verl/third_party/vllm/vllm_v_0_3_1/arg_utils.py
./verl/third_party/vllm/vllm_v_0_3_1/config.py
./verl/third_party/vllm/vllm_v_0_3_1/llm.py
./verl/third_party/vllm/vllm_v_0_3_1/llm_engine_sp.py
./verl/third_party/vllm/vllm_v_0_3_1/model_loader.py
./verl/third_party/vllm/vllm_v_0_3_1/model_runner.py
./verl/third_party/vllm/vllm_v_0_3_1/parallel_state.py
./verl/third_party/vllm/vllm_v_0_3_1/tokenizer.py
./verl/third_party/vllm/vllm_v_0_3_1/weight_loaders.py
./verl/third_party/vllm/vllm_v_0_3_1/worker.py
./verl/third_party/vllm/vllm_v_0_4_2/__init__.py
./verl/third_party/vllm/vllm_v_0_4_2/arg_utils.py
./verl/third_party/vllm/vllm_v_0_4_2/config.py
./verl/third_party/vllm/vllm_v_0_4_2/dtensor_weight_loaders.py
./verl/third_party/vllm/vllm_v_0_4_2/hf_weight_loader.py
./verl/third_party/vllm/vllm_v_0_4_2/llm.py
./verl/third_party/vllm/vllm_v_0_4_2/llm_engine_sp.py
./verl/third_party/vllm/vllm_v_0_4_2/megatron_weight_loaders.py
./verl/third_party/vllm/vllm_v_0_4_2/model_loader.py
./verl/third_party/vllm/vllm_v_0_4_2/model_runner.py
./verl/third_party/vllm/vllm_v_0_4_2/parallel_state.py
./verl/third_party/vllm/vllm_v_0_4_2/spmd_gpu_executor.py
./verl/third_party/vllm/vllm_v_0_4_2/tokenizer.py
./verl/third_party/vllm/vllm_v_0_4_2/worker.py
./verl/third_party/vllm/vllm_v_0_5_4/__init__.py
./verl/third_party/vllm/vllm_v_0_5_4/arg_utils.py
./verl/third_party/vllm/vllm_v_0_5_4/config.py
./verl/third_party/vllm/vllm_v_0_5_4/dtensor_weight_loaders.py
./verl/third_party/vllm/vllm_v_0_5_4/hf_weight_loader.py
./verl/third_party/vllm/vllm_v_0_5_4/llm.py
./verl/third_party/vllm/vllm_v_0_5_4/llm_engine_sp.py
./verl/third_party/vllm/vllm_v_0_5_4/megatron_weight_loaders.py
./verl/third_party/vllm/vllm_v_0_5_4/model_loader.py
./verl/third_party/vllm/vllm_v_0_5_4/model_runner.py
./verl/third_party/vllm/vllm_v_0_5_4/parallel_state.py
./verl/third_party/vllm/vllm_v_0_5_4/spmd_gpu_executor.py
./verl/third_party/vllm/vllm_v_0_5_4/tokenizer.py
./verl/third_party/vllm/vllm_v_0_5_4/worker.py
./verl/third_party/vllm/vllm_v_0_6_3/__init__.py
./verl/third_party/vllm/vllm_v_0_6_3/arg_utils.py
./verl/third_party/vllm/vllm_v_0_6_3/config.py
./verl/third_party/vllm/vllm_v_0_6_3/dtensor_weight_loaders.py
./verl/third_party/vllm/vllm_v_0_6_3/hf_weight_loader.py
./verl/third_party/vllm/vllm_v_0_6_3/llm.py
./verl/third_party/vllm/vllm_v_0_6_3/llm_engine_sp.py
./verl/third_party/vllm/vllm_v_0_6_3/megatron_weight_loaders.py
./verl/third_party/vllm/vllm_v_0_6_3/model_loader.py
./verl/third_party/vllm/vllm_v_0_6_3/model_runner.py
./verl/third_party/vllm/vllm_v_0_6_3/parallel_state.py
./verl/third_party/vllm/vllm_v_0_6_3/spmd_gpu_executor.py
./verl/third_party/vllm/vllm_v_0_6_3/tokenizer.py
./verl/third_party/vllm/vllm_v_0_6_3/worker.py
./verl/trainer/__init__.py
./verl/trainer/fsdp_sft_trainer.py
./verl/trainer/main_eval.py
./verl/trainer/main_generation.py
./verl/trainer/main_ppo.py
./verl/trainer/config/evaluation.yaml
./verl/trainer/config/generation.yaml
./verl/trainer/config/ppo_megatron_trainer.yaml
./verl/trainer/config/ppo_trainer.yaml
./verl/trainer/config/sft_trainer.yaml
./verl/trainer/ppo/__init__.py
./verl/trainer/ppo/core_algos.py
./verl/trainer/ppo/ray_trainer.py
./verl/utils/__init__.py
./verl/utils/config.py
./verl/utils/distributed.py
./verl/utils/flops_counter.py
./verl/utils/fs.py
./verl/utils/fsdp_utils.py
./verl/utils/hdfs_io.py
./verl/utils/import_utils.py
./verl/utils/logging_utils.py
./verl/utils/megatron_utils.py
./verl/utils/memory_buffer.py
./verl/utils/model.py
./verl/utils/py_functional.py
./verl/utils/ray_utils.py
./verl/utils/seqlen_balancing.py
./verl/utils/tokenizer.py
./verl/utils/torch_dtypes.py
./verl/utils/torch_functional.py
./verl/utils/tracking.py
./verl/utils/ulysses.py
./verl/utils/dataset/__init__.py
./verl/utils/dataset/rl_dataset.py
./verl/utils/dataset/rm_dataset.py
./verl/utils/debug/__init__.py
./verl/utils/debug/performance.py
./verl/utils/debug/trajectory_tracker.py
./verl/utils/logger/__init__.py
./verl/utils/logger/aggregate_logger.py
./verl/utils/megatron/__init__.py
./verl/utils/megatron/memory.py
./verl/utils/megatron/optimizer.py
./verl/utils/megatron/optimizer_config.py
./verl/utils/megatron/pipeline_parallel.py
./verl/utils/megatron/sequence_parallel.py
./verl/utils/megatron/tensor_parallel.py
./verl/utils/rendezvous/__init__.py
./verl/utils/rendezvous/ray_backend.py
./verl/utils/reward_score/__init__.py
./verl/utils/reward_score/countdown.py
./verl/utils/reward_score/gsm8k.py
./verl/utils/reward_score/math.py
./verl/utils/reward_score/multiply.py
./verl/utils/reward_score/qa_em.py
./verl/version/version
./verl/workers/__init__.py
./verl/workers/fsdp_workers.py
./verl/workers/megatron_workers.py
./verl/workers/retriever_workers.py
./verl/workers/actor/__init__.py
./verl/workers/actor/base.py
./verl/workers/actor/dp_actor.py
./verl/workers/actor/megatron_actor.py
./verl/workers/critic/__init__.py
./verl/workers/critic/base.py
./verl/workers/critic/dp_critic.py
./verl/workers/critic/megatron_critic.py
./verl/workers/reward_model/__init__.py
./verl/workers/reward_model/base.py
./verl/workers/reward_model/megatron/__init__.py
./verl/workers/reward_model/megatron/reward_model.py
./verl/workers/rollout/__init__.py
./verl/workers/rollout/base.py
./verl/workers/rollout/hf_rollout.py
./verl/workers/rollout/tokenizer.py
./verl/workers/rollout/naive/__init__.py
./verl/workers/rollout/naive/naive_rollout.py
./verl/workers/rollout/vllm_rollout/__init__.py
./verl/workers/rollout/vllm_rollout/vllm_rollout.py
./verl/workers/sharding_manager/__init__.py
./verl/workers/sharding_manager/base.py
./verl/workers/sharding_manager/fsdp_ulysses.py
./verl/workers/sharding_manager/fsdp_vllm.py
./verl/workers/sharding_manager/megatron_vllm.py
verl.egg-info/PKG-INFO
verl.egg-info/SOURCES.txt
verl.egg-info/dependency_links.txt
verl.egg-info/requires.txt
verl.egg-info/top_level.txt
verl/version/version