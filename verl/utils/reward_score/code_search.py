from typing import List
import re
import random
import json
import traceback


def extract_files_from_ground_truth(ground_truth: List[str]) -> List[str]:
    file_paths = []

    for answer in ground_truth:
        file_path = answer.strip().split(':')[0]
        if file_path:
            file_paths.append(file_path)

    return list(set(file_paths))


def extract_files_from_search_results(solution_str: str) -> List[str]:
    info_pattern = r'<answer>(.*?)</answer>'
    match = re.finditer(info_pattern, solution_str, re.DOTALL)
    matches = list(match)

    if not matches:
        print('----------No answers matched!----------')   
        return []

    match = matches[-1]
    print('----------Answer matched----------')
    print(match.group(1))

    try:
        match = json.loads(match.group(1).strip())
        file_paths = [m['path'] for m in match]
        return list(set(file_paths))
    except Exception as e:
        print(e)
        print(traceback.format_exc())
        print()
        return []


def calculate_f_score(
        searched_files: List[str],
        ref_files: List[str],
        beta: float = 2.0
) -> float:
    searched_files = set(searched_files)
    ref_files = set(ref_files)
    tp = len(searched_files.intersection(ref_files))
    precision = tp / len(searched_files) if len(searched_files) > 0 else 0
    recall = tp / len(ref_files) if len(ref_files) > 0 else 0
    score = (
        (1 + beta ** 2) * (precision * recall) / (precision * (beta ** 2) + recall)
        if (precision + recall) > 0
        else 0
    )
    return score


def compute_score(
        solution_str,
        ground_truth,
        method='strict',
        format_score=0.1,
        score=1.
):
    """The scoring function for substring exact match (EM).

    Args:
        solution_str: the solution text
        ground_truth: the ground truth
        method: the method to extract the solution, choices are 'strict' and 'flexible'
        format_score: the score for the format
        score: the score for the correct answer
    """
    print('{}{}{}'.format('-'*25, 'solution_str', '-'*25))
    print(solution_str)

    searched_files = extract_files_from_search_results(solution_str=solution_str)
    ref_files = extract_files_from_ground_truth(ground_truth=ground_truth['target'])


    print('{}{}{}'.format('-'*25, 'results', '-'*25))
    print(f"Golden answers: {ref_files}")
    print(f"Extracted answer: {searched_files}")

    if len(searched_files) == 0:
        score = 0
    else:
        score = max(calculate_f_score(searched_files, ref_files), format_score)

    print('{}{}{}'.format('-'*25, 'score', '-'*25))
    print(score)

    return score
