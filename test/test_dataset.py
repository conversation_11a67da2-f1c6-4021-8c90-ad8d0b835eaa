from verl.utils.dataset.rl_dataset import RLHFDataset


def test():
    dataset = RLHFDataset(
        parquet_files=self.config.data.train_files,
                                             tokenizer=self.tokenizer,
                                             prompt_key=self.config.data.prompt_key,
                                             max_prompt_length=self.config.data.max_prompt_length,
                                             filter_prompts=True,
                                             return_raw_chat=self.config.data.get('return_raw_chat',
                                                                                  False),
                                             truncation='error'
    )